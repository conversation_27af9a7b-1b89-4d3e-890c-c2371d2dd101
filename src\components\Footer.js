import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-[#008000] text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-bold mb-4">EEU</h3>
            <p className="text-gray-400">Helping communities stay informed about power outages.</p>
          </div>
          <div>
            <h4 className="text-md font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><a href="#report" className="text-gray-400 hover:text-white transition">Report Outage</a></li>
              <li><a href="#status" className="text-gray-400 hover:text-white transition">Outage Map</a></li>
              <li><a href="#faq" className="text-gray-400 hover:text-white transition">FAQ</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-md font-semibold mb-4">Contact Us</h4>
            <ul className="space-y-2 text-gray-400">
              <li className="flex items-center"><i className="fas fa-phone-alt mr-2"></i><span>1-800-PWR-WATCH</span></li>
              <li className="flex items-center"><i className="fas fa-envelope mr-2"></i><span><EMAIL></span></li>
              <li className="flex items-center"><i className="fas fa-map-marker-alt mr-2"></i><span>123 Utility St, Power City</span></li>
            </ul>
          </div>
          <div>
            <h4 className="text-md font-semibold mb-4">Follow Us</h4>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition"><i className="fab fa-facebook-f text-xl"></i></a>
              <a href="#" className="text-gray-400 hover:text-white transition"><i className="fab fa-twitter text-xl"></i></a>
              <a href="#" className="text-gray-400 hover:text-white transition"><i className="fab fa-instagram text-xl"></i></a>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2023 EEU. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;