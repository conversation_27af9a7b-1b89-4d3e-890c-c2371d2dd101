import React, { useState } from 'react';

const sampleOutages = [
  { id: 1, address: "123 Main St", status: "investigating" },
  { id: 2, address: "456 Oak Ave", status: "repairing" },
  { id: 3, address: "789 Pine Rd", status: "restored" }
];

const getStatusBadgeClass = (status) => {
  const classes = {
    investigating: "bg-[#FAD7A0] text-[#FFA500]",
    repairing: "bg-[#FFA500] text-white",
    restored: "bg-[#008000] text-white"
  };
  return classes[status] || "bg-gray-100 text-gray-800";
};

const getStatusText = (status) => {
  const texts = {
    investigating: "Investigating",
    repairing: "Repairing",
    restored: "Restored"
  };
  return texts[status] || status;
};

const CheckStatus = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const filtered = sampleOutages.filter(outage =>
    outage.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <section id="status" className="mb-16">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Check Outage Status</h2>
      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-medium text-gray-800">Current Outages</h3>
            </div>
            <div className="relative w-full md:w-64">
              <input
                type="text"
                placeholder="Search by address..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <i className="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
          </div>
        </div>
        <div className="divide-y divide-gray-200">
          {filtered.map((outage) => (
            <div key={outage.id} className="p-6 hover:bg-gray-50 cursor-pointer">
              <div className="flex justify-between items-center">
                <h3 className="font-medium text-gray-800">{outage.address}</h3>
                <span className={`status-badge ${getStatusBadgeClass(outage.status)} px-3 py-1 rounded-full text-sm font-semibold`}>
                  {getStatusText(outage.status)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CheckStatus;