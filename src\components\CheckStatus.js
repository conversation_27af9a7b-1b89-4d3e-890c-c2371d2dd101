import React, { useState, useEffect, useCallback } from 'react';

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const getStatusBadgeClass = (status) => {
  const classes = {
    received: "bg-blue-100 text-blue-800",
    investigating: "bg-[#FAD7A0] text-[#FFA500]",
    in_progress: "bg-orange-100 text-orange-800",
    repairing: "bg-[#FFA500] text-white",
    resolved: "bg-green-100 text-green-800",
    restored: "bg-[#008000] text-white",
    closed: "bg-gray-100 text-gray-800",
    pending: "bg-purple-100 text-purple-800"
  };
  return classes[status?.toLowerCase()] || "bg-gray-100 text-gray-800";
};

const getStatusText = (status) => {
  const texts = {
    received: "Received",
    investigating: "Investigating",
    in_progress: "In Progress",
    resolved: "Resolved",
    closed: "Closed",
    pending: "Pending"
  };
  return texts[status?.toLowerCase()] || status;
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const CheckStatus = () => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [statusOptions, setStatusOptions] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const pageSize = 3; // Show only 3 messages per page

  const fetchMessages = useCallback(async (filters = {}, page) => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      const pageToUse = page !== undefined ? page : currentPage;

      console.log('Fetching page:', pageToUse, 'Current Page:', currentPage); // Debug log

      // Add pagination parameters
      queryParams.append('page', pageToUse.toString());
      queryParams.append('page_size', pageSize.toString());

      if (searchTerm) {
        queryParams.append('search', searchTerm);
      }

      if (statusFilter) {
        queryParams.append('status', statusFilter);
      }

      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      console.log('API Query Params:', queryParams.toString()); // Debug log
      console.log('Full API URL:', `${API_BASE_URL}/report/messages/?${queryParams}`); // Debug log

      const response = await fetch(`${API_BASE_URL}/report/messages/?${queryParams}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data); // Debug log

      if (data.results) {
        setMessages(data.results);
        setTotalCount(data.count || 0);
        setTotalPages(Math.ceil((data.count || 0) / pageSize));
        console.log('Paginated response - Count:', data.count, 'Pages:', Math.ceil((data.count || 0) / pageSize));
      } else if (Array.isArray(data)) {
        let allMessages = [...data];
        let totalMessages = allMessages.length;

        // FOR TESTING: If we have fewer than 6 messages, create some mock data to test pagination
        if (totalMessages < 6) {
          const mockMessages = [];
          for (let i = 0; i < 10; i++) {
            mockMessages.push({
              id: `mock-${i}`,
              complaint: `Test Complaint ${i + 1}`,
              sender: `Test User ${i + 1}`,
              address: `Test Address ${i + 1}`,
              canumber: `CA${1000 + i}`,
              status: ['received', 'investigating', 'resolved'][i % 3],
              content: `This is test content for message ${i + 1}`,
              read: i % 2 === 0,
              starred: i % 3 === 0,
              created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
              updated_at: new Date(Date.now() - i * 12 * 60 * 60 * 1000).toISOString()
            });
          }
          allMessages = [...allMessages, ...mockMessages];
          totalMessages = allMessages.length;
          console.log('Added mock data for testing pagination');
        }

        const startIndex = (pageToUse - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedMessages = allMessages.slice(startIndex, endIndex);

        setMessages(paginatedMessages);
        setTotalCount(totalMessages);
        setTotalPages(Math.ceil(totalMessages / pageSize));

        console.log('Non-paginated response - Total:', totalMessages, 'Pages:', Math.ceil(totalMessages / pageSize));
      } else {
        setMessages([data]);
        setTotalCount(1);
        setTotalPages(1);
      }

      setError(null);
    } catch (err) {
      setError(`Failed to fetch messages: ${err.message}`);
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter, currentPage, pageSize]);

  const fetchFilterOptions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/report/messages/filter_options/`);
      console.log('Response:', response);
      if (response.ok) {
        const data = await response.json();
        setStatusOptions(data.status_options || []);
      }
    } catch (err) {
      console.error('Error fetching filter options:', err);
    }
  };

  useEffect(() => {
    fetchMessages({}, 1);
    fetchFilterOptions();
  }, [fetchMessages]);

  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [searchTerm, statusFilter]);

  useEffect(() => {
    console.log('Page changed to:', currentPage); // Debug log
    fetchMessages({}, currentPage);
  }, [currentPage, fetchMessages]);

  const handleSearchInput = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchMessages({}, 1);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSearchSubmit(e);
    }
  };

  const handleStatusFilter = (e) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleRefresh = () => {
    fetchMessages({}, currentPage);
  };

  const handlePageChange = (newPage) => {
    console.log('Changing to page:', newPage);
    setCurrentPage(newPage);
  };

  const handlePreviousPage = () => {
    console.log('Previous page clicked, current:', currentPage);
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    console.log('Next page clicked, current:', currentPage, 'total:', totalPages);
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  if (loading && messages.length === 0) {
    return (
      <section id="status" className="mb-16">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Check Status</h2>
        <div className="bg-white rounded-xl shadow-md p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading messages...</p>
        </div>
      </section>
    );
  }

  return (
    <section id="status" className="mb-16">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Check Status</h2>

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        {/* Header with search and filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-800">Reports & Complaints</h3>
              <button
                onClick={handleRefresh}
                className="ml-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                title="Refresh"
              >
                <i className="fas fa-sync-alt"></i>
              </button>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              {/* Search Input with Button */}
              <form onSubmit={handleSearchSubmit} className="flex items-center mt-3">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search messages..."
                    value={searchTerm}
                    onChange={handleSearchInput}
                    onKeyDown={handleKeyDown}
                    className="w-full sm:w-64 pl-10 pr-4 py-2 h-10 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <button
                  type="submit"
                  className="px-4 py-2 h-10 bg-blue-500 text-white rounded-r-lg hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                  title="Search"
                >
                  <i className="fas fa-search"></i>
                </button>
              </form>
              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={handleStatusFilter}
                className="px-4 py-2 h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                {statusOptions.map((status) => (
                  <option key={status} value={status}>
                    {getStatusText(status)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Messages List */}
        <div className="divide-y divide-gray-200">
          {loading && messages.length > 0 && (
            <div className="p-4 bg-blue-50 text-center">
              <i className="fas fa-spinner fa-spin text-blue-500 mr-2"></i>
              <span className="text-blue-700">Updating...</span>
            </div>
          )}
          {messages.length === 0 && !loading ? (
            <div className="p-8 text-center text-gray-500">
              <i className="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
              <p className="text-lg">No messages found</p>
              <p className="text-sm">Try adjusting your search or filters</p>
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 mb-1">
                          {message.complaint}
                          {message.starred && (
                            <i className="fas fa-star text-yellow-500 ml-2"></i>
                          )}
                          {!message.read && (
                            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                          )}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>From:</strong> {message.sender}
                        </p>
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>Address:</strong> {message.address}
                        </p>
                        {message.canumber && (
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>CA Number:</strong> {message.canumber}
                          </p>
                        )}
                        {message.requestNumber && (
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>Request #:</strong> {message.requestNumber}
                          </p>
                        )}
                        {message.content && (
                          <p className="text-sm text-gray-700 mt-2 p-3 bg-gray-50 rounded-lg">
                            {message.content}
                          </p>
                        )}
                        {message.remark && (
                          <p className="text-sm text-blue-700 mt-2 p-3 bg-blue-50 rounded-lg">
                            <strong>Remark:</strong> {message.remark}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center text-xs text-gray-500 gap-4">
                      <span>Created: {formatDate(message.created_at)}</span>
                      {message.updated_at !== message.created_at && (
                        <span>Updated: {formatDate(message.updated_at)}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusBadgeClass(message.status)}`}>
                      {getStatusText(message.status)}
                    </span>
                    <div className="text-xs text-gray-500">
                      ID: {message.id}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Debug Info - Remove this in production */}
        {/* <div className="px-6 py-2 bg-yellow-50 border-t border-yellow-200 text-xs text-yellow-800">
          Debug: Total Count: {totalCount}, Total Pages: {totalPages}, Current Page: {currentPage}, Page Size: {pageSize}
        </div> */}

        {/* Pagination - Always show when we have messages */}
        {totalCount > 0 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 justify-end">
              <div className="flex items-center space-x-2 ">
                <button
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                {/* Page numbers */}
                <div className="flex space-x-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-1 rounded-md text-sm font-medium ${
                        page === currentPage
                          ? 'bg-blue-500 text-white'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>
                <button
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default CheckStatus;