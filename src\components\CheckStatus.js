import React, { useState, useEffect, useCallback } from 'react';

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const getStatusBadgeClass = (status) => {
  const classes = {
    received: "bg-blue-100 text-blue-800",
    investigating: "bg-yellow-100 text-yellow-800",
    in_progress: "bg-orange-100 text-orange-800",
    resolved: "bg-green-100 text-green-800",
    closed: "bg-gray-100 text-gray-800",
    pending: "bg-purple-100 text-purple-800"
  };
  return classes[status?.toLowerCase()] || "bg-gray-100 text-gray-800";
};

const getStatusText = (status) => {
  const texts = {
    received: "Received",
    investigating: "Investigating",
    in_progress: "In Progress",
    resolved: "Resolved",
    closed: "Closed",
    pending: "Pending"
  };
  return texts[status?.toLowerCase()] || status;
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const CheckStatus = () => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [statusOptions, setStatusOptions] = useState([]);
  const [statistics, setStatistics] = useState(null);

  // Fetch messages from Django API
  const fetchMessages = useCallback(async (filters = {}) => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      // Add search term
      if (searchTerm) {
        queryParams.append('search', searchTerm);
      }

      // Add status filter
      if (statusFilter) {
        queryParams.append('status', statusFilter);
      }

      // Add any additional filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      const response = await fetch(`http://localhost:8000/api/report/messages/?${queryParams}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setMessages(data.results || data);
      setError(null);
    } catch (err) {
      setError(`Failed to fetch messages: ${err.message}`);
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter]);

  // Fetch filter options
  const fetchFilterOptions = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/report/messages/filter_options/`);
      console.log('Response:', response);
      if (response.ok) {
        const data = await response.json();
        setStatusOptions(data.status_options || []);
      }
    } catch (err) {
      console.error('Error fetching filter options:', err);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/messages/statistics/`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data);
      }
    } catch (err) {
      console.error('Error fetching statistics:', err);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchMessages();
    fetchFilterOptions();
    fetchStatistics();
  }, [fetchMessages]);

  // Refetch when status filter changes
  useEffect(() => {
    fetchMessages();
  }, [statusFilter, fetchMessages]);

  const handleSearchInput = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    fetchMessages();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSearchSubmit(e);
    }
  };

  const handleStatusFilter = (e) => {
    setStatusFilter(e.target.value);
  };

  const handleRefresh = () => {
    fetchMessages();
    fetchStatistics();
  };

  if (loading && messages.length === 0) {
    return (
      <section id="status" className="mb-16">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Check Status</h2>
        <div className="bg-white rounded-xl shadow-md p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading messages...</p>
        </div>
      </section>
    );
  }

  return (
    <section id="status" className="mb-16">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Check Status</h2>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-sm font-medium text-gray-500">Total Messages</h3>
            <p className="text-2xl font-bold text-gray-900">{statistics.total_messages}</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-sm font-medium text-gray-500">Unread</h3>
            <p className="text-2xl font-bold text-red-600">{statistics.unread_messages}</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-sm font-medium text-gray-500">Read</h3>
            <p className="text-2xl font-bold text-green-600">{statistics.read_messages}</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-sm font-medium text-gray-500">Starred</h3>
            <p className="text-2xl font-bold text-yellow-600">{statistics.starred_messages}</p>
          </div>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        {/* Header with search and filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-800">Messages & Complaints</h3>
              <button
                onClick={handleRefresh}
                className="ml-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                title="Refresh"
              >
                <i className="fas fa-sync-alt"></i>
              </button>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 items-center">
              {/* Search Input with Button */}
              <form onSubmit={handleSearchSubmit} className="flex items-center">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search messages..."
                    value={searchTerm}
                    onChange={handleSearchInput}
                    onKeyDown={handleKeyDown}
                    className="w-full sm:w-64 pl-10 pr-4 py-2 h-10 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <button
                  type="submit"
                  className="px-4 py-2 h-10 bg-blue-500 text-white rounded-r-lg hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                  title="Search"
                >
                  <i className="fas fa-search"></i>
                </button>
              </form>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={handleStatusFilter}
                className="px-4 py-2 h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                {statusOptions.map((status) => (
                  <option key={status} value={status}>
                    {getStatusText(status)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Messages List */}
        <div className="divide-y divide-gray-200">
          {loading && messages.length > 0 && (
            <div className="p-4 bg-blue-50 text-center">
              <i className="fas fa-spinner fa-spin text-blue-500 mr-2"></i>
              <span className="text-blue-700">Updating...</span>
            </div>
          )}

          {messages.length === 0 && !loading ? (
            <div className="p-8 text-center text-gray-500">
              <i className="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
              <p className="text-lg">No messages found</p>
              <p className="text-sm">Try adjusting your search or filters</p>
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 mb-1">
                          {message.complaint}
                          {message.starred && (
                            <i className="fas fa-star text-yellow-500 ml-2"></i>
                          )}
                          {!message.read && (
                            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                          )}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>From:</strong> {message.sender} ({message.phone})
                        </p>
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>Address:</strong> {message.address}
                        </p>
                        {message.canumber && (
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>CA Number:</strong> {message.canumber}
                          </p>
                        )}
                        {message.requestNumber && (
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>Request #:</strong> {message.requestNumber}
                          </p>
                        )}
                        {message.content && (
                          <p className="text-sm text-gray-700 mt-2 p-3 bg-gray-50 rounded-lg">
                            {message.content}
                          </p>
                        )}
                        {message.remark && (
                          <p className="text-sm text-blue-700 mt-2 p-3 bg-blue-50 rounded-lg">
                            <strong>Remark:</strong> {message.remark}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center text-xs text-gray-500 gap-4">
                      <span>Created: {formatDate(message.created_at)}</span>
                      {message.updated_at !== message.created_at && (
                        <span>Updated: {formatDate(message.updated_at)}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col items-end gap-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusBadgeClass(message.status)}`}>
                      {getStatusText(message.status)}
                    </span>
                    <div className="text-xs text-gray-500">
                      ID: {message.id}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </section>
  );
};

export default CheckStatus;