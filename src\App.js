// import React, { useState } from 'react';
// import Header from './components/Header';
// import HeroSection from './components/HeroSection';
// import ReportOutage from './components/ReportOutage';
// import CheckStatus from './components/CheckStatus';
// import FAQ from './components/FAQ';
// import EmergencyBanner from './components/EmergencyBanner';
// import Footer from './components/Footer';
// import Login from './components/Login';
// import Signup from './components/Signup';

// function App() {
//   const [user] = useState(null); // Replace with actual auth logic
//   const [loginModalOpen, setLoginModalOpen] = useState(false);
//   const [signupModalOpen, setSignupModalOpen] = useState(false);

//  const handleLogin = (userData) => {
//     setUser(userData);
//     setFormData((prev) => ({
//       ...prev,
//       name: userData.name || '',
//       phone: userData.phone || '',
//       caBpNumber: userData.caBpNumber || ''
//     }));
//     setLoginModalOpen(false);
//   };

//   const handleSignup = (userData) => {
//     setUser(userData);
//     setFormData((prev) => ({
//       ...prev,
//       name: userData.name || '',
//       phone: userData.phone || '',
//       caBpNumber: userData.caBpNumber || ''
//     }));
//     setSignupModalOpen(false);
//   };

//   const handleLogout = () => {
//     localStorage.removeItem('EEU_user');
//     setUser(null);
//     setFormData({
//       name: '',
//       phone: '',
//       caBpNumber: '',
//       address: '',
//       details: ''
//     });
//     setLocation(null);
//   };

//   const switchToSignup = () => {
//     setLoginModalOpen(false);
//     setSignupModalOpen(true);
//   };

//   const switchToLogin = () => {
//     setSignupModalOpen(false);
//     setLoginModalOpen(true);
//   };

//   return (
//     <div className="bg-gray-50 min-h-screen">
//       <Header
//         user={user}
//         handleLogout={handleLogout}
//         setLoginModalOpen={setLoginModalOpen}
//         setSignupModalOpen={setSignupModalOpen}
//       />
//       <HeroSection />
//       <main className="container mx-auto px-4 py-12">
//         <ReportOutage />
//         <CheckStatus />
//         <FAQ />
//         <EmergencyBanner />
//       </main>
//       <Footer />


//       {/* Modals */}
//       {loginModalOpen && (
//         <Login
//           onClose={() => setLoginModalOpen(false)}
//           onSwitchToSignup={switchToSignup}
//           onLogin={handleLogin}
//         />
//       )}
//       {signupModalOpen && (
//         <Signup
//           onClose={() => setSignupModalOpen(false)}
//           onSwitchToLogin={switchToLogin}
//           onSignup={handleSignup}
//         />
//       )}
//     </div>
//   );
// }

// export default App;




import React, { useState } from 'react';
import Header from './components/Header';
import HeroSection from './components/HeroSection';
import ReportOutage from './components/ReportOutage';
import CheckStatus from './components/CheckStatus';
import FAQ from './components/FAQ';
import EmergencyBanner from './components/EmergencyBanner';
import Footer from './components/Footer';
import Login from './components/Login';
import Signup from './components/Signup';

function App() {
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [signupModalOpen, setSignupModalOpen] = useState(false);

  return (
    <div className="bg-gray-50 min-h-screen">
      <Header
        user={null} // Replace this later when auth is implemented
        handleLogout={() => {
          localStorage.removeItem('EEU_user');
        }}
        setLoginModalOpen={setLoginModalOpen}
        setSignupModalOpen={setSignupModalOpen}
      />
      <HeroSection />
      <main className="container mx-auto px-4 py-12">
        <ReportOutage
          setLoginModalOpen={setLoginModalOpen}
          setSignupModalOpen={setSignupModalOpen}
        />
        <CheckStatus />
        <FAQ />
        <EmergencyBanner />
      </main>
      <Footer />

      {/* Modals */}
      {loginModalOpen && (
        <Login
          onClose={() => setLoginModalOpen(false)}
          onSwitchToSignup={setSignupModalOpen}
          onLogin={(userData) => {
            localStorage.setItem('EEU_user', JSON.stringify(userData));
            window.location.reload(); // Optional: reload to update UI
          }}
        />
      )}
      {signupModalOpen && (
        <Signup
          onClose={() => setSignupModalOpen(false)}
          onSwitchToLogin={() => {
            setSignupModalOpen(false);
            setLoginModalOpen(true);
          }}
          onSignup={(userData) => {
            localStorage.setItem('EEU_user', JSON.stringify(userData));
            window.location.reload(); // Optional: reload to update UI
          }}
        />
      )}
    </div>
  );
}

export default App;




