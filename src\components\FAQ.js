import React, { useState } from 'react';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqs = [
    { question: "How long does it typically take to restore power?", answer: "Most simple outages are restored within 2-4 hours..." },
    { question: "Will I be notified when power is restored?", answer: "Yes, if you provide your email or phone number..." },
    { question: "What should I do if I see a downed power line?", answer: "Stay away from downed lines and call emergency services..." },
    { question: "How can I prepare for potential power outages?", answer: "Have flashlights, batteries, water, and food ready..." }
  ];

  return (
    <section id="faq" className="mb-16">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Frequently Asked Questions</h2>
      <div className="bg-white rounded-xl shadow-md overflow-hidden divide-y divide-gray-200">
        {faqs.map((item, index) => (
          <div key={index} className="p-6">
            <button onClick={() => toggleFAQ(index)} className="w-full flex justify-between items-center text-left">
              <h3 className="text-lg font-medium text-gray-800">{item.question}</h3>
              <i className={`fas fa-chevron-down text-blue-500 transition-transform ${openIndex === index ? 'rotate-180' : ''}`}></i>
            </button>
            {openIndex === index && <p className="mt-3 text-gray-600">{item.answer}</p>}
          </div>
        ))}
      </div>
    </section>
  );
};

export default FAQ;