import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageSelector from './LanguageSelector';

const Header = ({ user, handleLogout, setLoginModalOpen, setSignupModalOpen }) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const { t } = useLanguage();

  return (
    <header className="bg-[#FFA500] text-white shadow-lg">
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex justify-between items-center w-full md:w-auto">
            {/* Logo */}
            <div className="flex items-center">
              <img src="/eeu_logo2.png" alt="logo" className="w-16 h-16 mr-3" />
              <div>
                <h1 className="text-2xl font-bold">{t('header.title')}</h1>
                <p className="text-sm opacity-90">{t('header.subtitle')}</p>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-white focus:outline-none"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {/* Hamburger Icon */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16m-7 6h7"
                  />
                )}
              </svg>
            </button>
          </div>

          {/* Navigation Links and Buttons - Conditionally shown on mobile */}
          <nav
            className={`${
              isMenuOpen ? 'block' : 'hidden'
            } w-full md:w-auto md:block mt-4 md:mt-0`}
          >
            <ul className="flex flex-col md:flex-row md:items-center md:space-x-6 space-y-4 md:space-y-0">
              <li>
                <a href="#report" className="hover:text-blue-200 transition block">
                  {t('header.nav.report')}
                </a>
              </li>
              <li>
                <a href="#status" className="hover:text-blue-200 transition block">
                  {t('header.nav.status')}
                </a>
              </li>
              <li>
                <a href="#faq" className="hover:text-blue-200 transition block">
                  {t('header.nav.faq')}
                </a>
              </li>
              <li>
                <LanguageSelector className="mt-2 md:mt-0" />
              </li>

              {/* {user ? (
                <li>
                  <button
                    onClick={handleLogout}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition w-full md:w-auto"
                  >
                    Logout
                  </button>
                </li>
              ) : (
                <>
                  <li>
                    <button
                      onClick={() => setLoginModalOpen(true)}
                      className="bg-[#FFA500] text-white px-4 py-2 rounded-lg hover:bg-[#E69500] transition w-full md:w-auto"
                    >
                      Login
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={() => setSignupModalOpen(true)}
                      className="bg-[#008000] text-white px-4 py-2 rounded-lg hover:bg-[#34cf43] transition w-full md:w-auto"
                    >
                      Sign Up
                    </button>
                  </li>
                </>
              )} */}
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;