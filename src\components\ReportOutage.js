import React, { useState, useEffect } from 'react';
import Login from './Login';
import Signup from './Signup';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

const ReportOutage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [signupModalOpen, setSignupModalOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [location, setLocation] = useState(null); // Store selected coordinates
  const [formData, setFormData] = useState({
    sender: '',
    phone: '',
    address: '',
    canumber: '',
    complaint: '',
    content: ''
  });

  // Load saved user on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('EEU_user');
    if (savedUser) {
      const userData = JSON.parse(savedUser);
      setUser(userData);
      setFormData(prev => ({
        ...prev,
        name: userData.name || '',
        phone: userData.phone || '',
        canumber: userData.canumber || ''
      }));
    }
  }, []);

  // // Initialize Leaflet map
  // useEffect(() => {
  //   const map = L.map('map').setView([8.8922, 39.7012], 6); // Centered on Ethiopia

  //   L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',  {
  //     attribution: '&copy; OpenStreetMap contributors'
  //   }).addTo(map);

  //   let marker = null;

  //   // Try to get user's current location
  //   if (navigator.geolocation) {
  //     navigator.geolocation.getCurrentPosition(
  //       (position) => {
  //         const lat = position.coords.latitude;
  //         const lng = position.coords.longitude;
  //         map.setView([lat, lng], 16);
  //         marker = L.marker([lat, lng], { draggable: true }).addTo(map);
  //         marker.bindPopup("You are here").openPopup();
  //         setLocation({ lat, lng });
  //       },
  //       (error) => {
  //         console.error("Error getting location:", error);
  //       }
  //     );
  //   }

  //   // Handle map click to place marker
  //   map.on('click', (e) => {
  //     const lat = e.latlng.lat;
  //     const lng = e.latlng.lng;

  //     if (marker) {
  //       map.removeLayer(marker);
  //     }

  //     marker = L.marker([lat, lng], { draggable: true }).addTo(map);
  //     marker.bindPopup("Selected location").openPopup();
  //     setLocation({ lat, lng });

  //     // Update when marker is dragged
  //     marker.on('dragend', () => {
  //       const newLatLng = marker.getLatLng();
  //       setLocation({ lat: newLatLng.lat, lng: newLatLng.lng });
  //     });
  //   });

  //   return () => {
  //     map.remove();
  //   };
  // }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // if (!location) {
    //   alert("Please select a location on the map.");
    //   return;
    // }

    // const fullData = {
    //   ...formData,
    //   latitude: location.lat,
    //   longitude: location.lng
    // };

    // console.log('Form submitted:', fullData);
    // setModalOpen(true);

    console.log('Form submitted:', formData);

    try {
    const response = await fetch('http://localhost:8000/api/report/messages/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

   if (response.ok) {
      setModalOpen(true);

      // Clear only case-specific fields for logged-in users
      if (user) {
        setFormData((prev) => ({
          ...prev,
          address: '',
          details: '',
          complain: ''
        }));
      } else {
        setFormData({
          sender: '',
          phone: '',
          canumber: '',
          address: '',
          complaint: '',
          content: ''
        });
      }
    } else {
      alert('Failed to submit outage report.');
    }
  } catch (error) {
    alert('Error submitting outage report.');
    console.error(error);
  }
};

  

//   return (
//     <>
//       {/* Your JSX remains unchanged */}
//       {/* You can paste the JSX part from your original file here */}
//       {/* Or continue using your existing return block */}
//     </>
//   );
// };



  return (
    <>
      <section id="report" className="mb-16">
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          {/* Authentication Header */}
          {/* <div className="bg-gray-50 px-8 py-4 border-b">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                {user ? (
                  <>
                    <div className="flex items-center space-x-2">
                      <div className="bg-[#FFA500] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">
                        {user.name.charAt(0).toUpperCase()}
                      </div>
                      <span className="text-gray-700">Welcome back, {user.name}!</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      <i className="fas fa-info-circle mr-1"></i>
                      Your info is pre-filled. Just add case details.
                    </div>
                  </>
                ) : (
                  <div className="flex items-center space-x-2">
                    <i className="fas fa-user-plus text-[#FFA500]"></i>
                    <span className="text-gray-600">Login for faster reporting (optional)</span>
                  </div>
                )}
              </div>
              <div className="flex space-x-2">
                {user ? (
                  <button
                    onClick={handleLogout}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition"
                  >
                    Logout
                  </button>
                ) : (
                  <>
                    <button
                      onClick={() => setLoginModalOpen(true)}
                      className="bg-[#FFA500] text-white px-4 py-2 rounded-lg hover:bg-[#E69500] transition"
                    >
                      Login
                    </button>
                    <button
                      onClick={() => setSignupModalOpen(true)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition"
                    >
                      Sign Up
                    </button>
                  </>
                )}
              </div>
            </div>
          </div> */}

          <div className="md:flex">
            <div className="md:w-1/2 p-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">Report a Power Outage</h2>
              {user && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center">
                    <i className="fas fa-check-circle text-green-500 mr-2"></i>
                    <span className="text-green-700 font-medium">Quick Report Mode</span>
                  </div>
                  <p className="text-green-600 text-sm mt-1">
                    Your personal details are saved. Just fill in the outage address and details below.
                  </p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
                    Your Name {user && <span className="text-green-600 text-sm">(saved)</span>}
                    <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="sender"
                    name="sender"
                    value={formData.sender}
                    onChange={handleInputChange}
                    maxLength="50"
                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      user ? 'bg-gray-50' : ''
                    }`}
                    placeholder={user ? "Auto-filled from your profile" : "Enter your name"}
                    required
                    readOnly={user}
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                    Phone Number {user && <span className="text-green-600 text-sm">(saved)</span>}
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative flex">
                    <div className="flex items-center px-3 py-2 bg-gray-100 border border-r-0 border-gray-300 rounded-l-lg">
                      <img
                        src="/ethiopia-flag.jpg"
                        alt="ET"
                        className="w-10 h-7 mr-2 object-cover rounded-sm"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'inline';
                        }}
                      />
                      <span className="text-sm font-bold text-gray-700 mr-2" style={{display: 'none'}}>ET</span>
                      <span className="text-gray-700 font-medium">+251</span>
                    </div>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone?.replace('+251', '') || ''}
                      onChange={(e) => {
                        const value = e.target.value.replace(/[^0-9]/g, '');
                        handleInputChange({
                          target: {
                            name: 'phone',
                            value: '+251' + value
                          }
                        });
                      }}
                      maxLength="9"
                      pattern="[0-9]+"
                      className={`flex-1 px-4 py-2 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        user ? 'bg-gray-50' : ''
                      }`}
                      placeholder={user ? "Auto-filled" : "912345678"}
                      required
                      readOnly={user}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="canumber" className="block text-gray-700 font-medium mb-2">
                    CA Number or BP Number {user && <span className="text-green-600 text-sm">(saved)</span>}
                     <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="canumber"
                    name="canumber"
                    value={formData.canumber}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, '');
                      if (value.length <= 12) {
                        handleInputChange({
                          target: {
                            name: 'canumber',
                            value: value
                          }
                        });
                      }
                    }}
                    maxLength="12"
                    pattern="^[0-9]{10}$|^[0-9]{12}$"
                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      user ? 'bg-gray-50' : ''
                    }`}
                    placeholder={user ? "Auto-filled from your profile" : "BP: 10 digits, CA: 12 digits"}
                    required
                    readOnly={user}
                  />
                  {formData.canumber && formData.canumber.length > 0 &&
                   formData.canumber.length !== 10 && formData.canumber.length !== 12 && (
                    <p className="text-red-500 text-sm mt-1">
                      Must be exactly 10 digits (BP Number) or 12 digits (CA Number)
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="address" className="block text-gray-700 font-medium mb-2">
                    Address <span className="text-red-500">*</span>
                    {user && <span className="text-blue-600 text-sm"> (enter case details)</span>}
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    maxLength="255"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter the address where outage occurred (max 255 characters)"
                    required
                  />
                </div>


                <div>
                  <label htmlFor="complain" className="block text-gray-700 font-medium mb-2">
                    Complaint <span className="text-red-500">*</span>
                    {user && <span className="text-blue-600 text-sm"> (enter case details)</span>}
                  </label>
                  <select
                    id="complaint"
                    name="complaint"
                    value={formData.complaint || ""}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="" disabled>Select a complaint type</option>
                    <option value="no supply">No Supply one house</option>
                    <option value="no supply">No Supply partially</option>
                    <option value="wirecut">Wirecut</option>
                    <option value="voltage drop">Voltage Drop</option>
                    <option value="emergency">Emergency</option>
                    {/* <option value="corruption">Corruption</option> */}
                  </select>
                </div>

                <div>
                  <label htmlFor="content" className="block text-gray-700 font-medium mb-2">
                    Additional Details
                    {user && <span className="text-blue-600 text-sm"> (describe this specific case)</span>}
                  </label>
                  <textarea
                    id="content"
                    name="content"
                    rows="3"
                    value={formData.content}
                    onChange={handleInputChange}
                    maxLength="1000"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe what happened, any visible damage, time of outage, etc. (max 1000 characters)"
                  />
                </div>

                <div className="pt-2">
                  <button
                    type="submit"
                    className="w-full bg-[#FFA500] text-white font-bold py-3 px-4 rounded-lg hover:bg-[#E69500] transition focus:outline-none focus:ring-2 focus:ring-[#FFA500] focus:ring-offset-2"
                  >
                    <i className="fas fa-paper-plane mr-2"></i>
                    Submit Outage Report
                  </button>
                </div>
              </form>
            </div>

            <div className="md:w-1/2 bg-gray-50 p-8">
              {/* <div id="map" className="map-container h-72 bg-gray-200 rounded-lg"></div> */}
              <div className="map-container h-96 bg-gray-200 rounded-lg">
                <img src="./report.png" alt="Map" className="w-full h-full object-cover rounded-lg" />
              </div>
              <div className="mt-6">
                <h3 className="font-bold text-lg text-gray-800 mb-3">Before You Report</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Check if your neighbors also have power</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Verify your circuit breakers haven't tripped</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Look for any visible damage to power lines</span>
                  </li>
                </ul>
                {!user && (
                  <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-800 mb-2">
                      <i className="fas fa-lightbulb mr-2"></i>
                      Pro Tip
                    </h4>
                    <p className="text-blue-700 text-sm">
                      Create an account to save your details and report outages faster in the future!
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Success Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 mx-4">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-green-100 p-3 rounded-full">
                <i className="fas fa-check-circle text-green-500 text-3xl"></i>
              </div>
            </div>
            <h3 className="text-xl font-bold text-center text-gray-800 mb-2">
              Report Submitted Successfully!
            </h3>
            <p className="text-gray-600 text-center mb-4">
              Thank you for your report. We have received your outage information and will investigate promptly.
            </p>
            {user && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <p className="text-blue-700 text-sm text-center">
                  <i className="fas fa-info-circle mr-1"></i>
                  Your personal details are saved for future reports!
                </p>
              </div>
            )}
            <div className="text-center">
              <button 
                onClick={() => setModalOpen(false)} 
                className="bg-[#FFA500] text-white font-bold py-2 px-6 rounded-lg hover:bg-[#E69500] transition"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      
    </>
  );
};

export default ReportOutage;