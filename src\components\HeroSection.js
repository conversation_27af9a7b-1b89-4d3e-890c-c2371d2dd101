import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const HeroSection = () => {
  const { t } = useLanguage();

  return (
    <section className="bg-gradient-to-r from-[#FFA500] to-[#FAD7A0] text-white py-16">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-4xl font-bold mb-4">{t('hero.title')}</h2>
        <p className="text-xl mb-8 max-w-2xl mx-auto">{t('hero.subtitle')}</p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <a href="#report" className="bg-white text-blue-600 font-bold py-3 px-6 rounded-lg hover:bg-blue-50 transition">
            {t('hero.cta')}
          </a>
          <a href="#status" className="bg-transparent border-2 border-white font-bold py-3 px-6 rounded-lg hover:bg-[#34cf43] transition">
            {t('header.nav.status')}
          </a>
        </div>
        <div className="mt-6">
          <p className="text-lg font-semibold text-red-200">
            {t('hero.emergency')}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;