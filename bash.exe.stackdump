Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028DF99, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A545 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA20  00021006B9A5 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFED2AF0000 ntdll.dll
7FFED1D60000 KERNEL32.DLL
7FFECFE70000 KERNELBASE.dll
7FFED22C0000 USER32.dll
7FFED0610000 win32u.dll
000210040000 msys-2.0.dll
7FFED2290000 GDI32.dll
7FFED0370000 gdi32full.dll
7FFECFDD0000 msvcp_win.dll
7FFED0250000 ucrtbase.dll
7FFED2830000 advapi32.dll
7FFED0AC0000 msvcrt.dll
7FFED2A00000 sechost.dll
7FFED05E0000 bcrypt.dll
7FFED1F10000 RPCRT4.dll
7FFECF380000 CRYPTBASE.DLL
7FFED0700000 bcryptPrimitives.dll
7FFED2030000 IMM32.DLL
