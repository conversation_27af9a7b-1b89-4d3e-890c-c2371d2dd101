export default {
  // Header
  header: {
    title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Humna Elektirikii Itoophiyaa",
    subtitle: "Sirna Gabaasa Humna Cituu",
    nav: {
      home: "<PERSON><PERSON>",
      report: "Gabaasa Cituu",
      status: "Haala Ilaali",
      faq: "<PERSON><PERSON><PERSON><PERSON> Irra Deebi'amu",
      contact: "Nu Quunnamaa"
    },
    auth: {
      login: "<PERSON><PERSON>",
      signup: "<PERSON><PERSON><PERSON><PERSON>'<PERSON>",
      logout: "Ba'<PERSON>",
      welcome: "<PERSON>ga nagaan dhufte, {{name}}!"
    }
  },

  // Hero Section
  hero: {
    title: "Cituu Humna Elektirikii Saffisaa fi Salphaadhaan Gabaasaa",
    subtitle: "Na<PERSON><PERSON> keessanitti cituu humna elektirikii gabaasuu keessaniin humni dafee akka deebi'u nu gargaaraa. Gareen keenya gabaasa hundaaf saffisaan deebii kenna.",
    cta: "Amma Gabaasaa",
    emergency: "<PERSON><PERSON><PERSON>? 911 bilbilaa"
  },

  // Report Outage
  report: {
    title: "Cituu Humna Elektirikii Gabaasaa",
    quickMode: "Haalata Gabaasa Saffisaa",
    quickModeDesc: "<PERSON>deef<PERSON><PERSON> keessan dhuunfaa kuufameera. Teessoo cituu fi bal'ina qofa guutaa.",
    fields: {
      name: "Maqaa Keessan",
      nameRequired: "Maqaan barbaachisaa dha",
      nameMax: "Maqaan arfii 50 ykn isaa gadi ta'uu qaba",
      phone: "Lakkoofsa Bilbilaa",
      phoneRequired: "Lakkoofsi bilbilaa barbaachisaa dha",
      phoneInvalid: "Maaloo lakkoofsa bilbilaa sirrii galchaa",
      caNumber: "Lakkoofsa CA ykn Lakkoofsa BP",
      caNumberRequired: "Lakkoofsi CA ykn BP barbaachisaa dha",
      caNumberInvalid: "Sirriitti lakkoofsota 10 (Lakkoofsa BP) ykn lakkoofsota 12 (Lakkoofsa CA) ta'uu qaba",
      address: "Teessoo",
      addressRequired: "Teessoon barbaachisaa dha",
      complaint: "Komii",
      complaintRequired: "Maaloo gosa komii filadhu",
      content: "Bal'ina Dabalataa",
      contentPlaceholder: "Maal akka ta'e, miidhaa mul'atu kamiyyuu, yeroo cituu, kkf ibsaa."
    },
    placeholders: {
      name: "Maqaa keessan galchaa (arfii 50 gadi)",
      phone: "912345678",
      caNumber: "BP: lakkoofsota 10, CA: lakkoofsota 12",
      address: "Teessoo cituu itti mudatee galchaa (arfii 150 gadi)",
      complaint: "Gosa komii filadhu"
    },
    complaintTypes: {
      noSupplyOne: "Mana tokko humni hin jiru",
      noSupplyPartial: "Humni gartokkee hin jiru",
      wirecut: "Sibiila muruu",
      voltageDrop: "Voltaajii hir'achuu",
      emergency: "Balaa"
    },
    submit: "Gabaasa Cituu Galchi",
    beforeReport: "Osoo Hin Gabaasiin Dura",
    checklist: [
      "Olloonni keessan humna qabuu fi dhiisuu ilaali",
      "Cituu daandii keessanii akka hin cite mirkaneessi",
      "Sibiila humna irratti miidhaa mul'atu barbaadi",
      "Naannoo keessanitti namoonni har'aaf gabaasa cituu qabuu fi dhiisuu ilaaluuf kutaa \"Haala Ilaali\" armaan gadii ilaali."
    ],
    success: {
      title: "Gabaasni Milkaa'inaan Ergame!",
      message: "Gabaasa keessaniif galatoomaa. Odeeffannoo cituu keessanii fudhannee saffisaan ni qorannaaa.",
      savedInfo: "Odeeffannoon keessan dhuunfaa gabaasalee gara fuulduraatiif kuufameera!",
      close: "Cufi"
    },
    errors: {
      submitFailed: "Galchuun Hin Milkoofne",
      submitFailedMessage: "Gabaasa cituu galchuun hin milkoofne. Maaloo irra deebi'aa yaali.",
      networkError: "Dogoggora Networkii",
      networkErrorMessage: "Gabaasa cituu galchuu dogoggora. Maaloo walitti dhufeenya keessan ilaalaatii irra deebi'aa yaali."
    }
  },

  // Check Status
  status: {
    title: "Haala Ilaali",
    subtitle: "Gabaasalee fi Komiiwwan",
    search: "Ergaawwan barbaadi...",
    searchPlaceholder: "Ergaawwan barbaadi...",
    filters: {
      allStatuses: "Haala Hunda",
      refresh: "Haaromsi"
    },
    statistics: {
      total: "Ergaawwan Waligalaa",
      unread: "Hin Dubbifamne",
      read: "Dubbifame",
      starred: "Urjii Godhamee"
    },
    messageDetails: {
      from: "Irraa",
      address: "Teessoo",
      caNumber: "Lakkoofsa CA",
      requestNumber: "Gaafata #",
      remark: "Yaadachiisa",
      created: "Uumame",
      updated: "Haaromfame",
      id: "Eenyummaa"
    },
    statuses: {
      received: "Fudhame",
      investigating: "Qorannoo keessa",
      in_progress: "Adeemsa keessa",
      resolved: "Furmaata argateera",
      closed: "Cufame",
      pending: "Eegaa jira"
    },
    pagination: {
      showing: "Fuula {{current}} {{total}} keessaa agarsiisaa (ergaawwan {{count}} waligalaa)",
      displaying: "Ergaawwan {{count}} agarsiisaa",
      previous: "Dura",
      next: "Itti Aansu"
    },
    noMessages: "Ergaawwan tokkollee hin argamne",
    noMessagesDesc: "Barbaacha ykn gingilchaa keessan sirreessuu yaali",
    loading: "Ergaawwan fe'aa jira..."
  },

  // FAQ
  faq: {
    title: "Gaaffii Irra Deebi'amu",
    questions: [
      {
        question: "Cituu koo hammam saffisaan deebi'a?",
        answer: "Cituuwwan hedduun sa'aatii 2-4 keessatti deebi'u. Haalli balaa yeruma sana xiyyeeffannoo argata. Adeemsa irratti isin beeksisna."
      },
      {
        question: "Haala gabaasa koo hordofuu nan danda'aa?",
        answer: "Eeyyee! Teessoo ykn lakkoofsa CA keessan fayyadamuun haala cituu naannoo keessanii fooyya'iinsa barbaaduuf kutaa 'Haala Ilaali' armaan olii fayyadamaa."
      },
      {
        question: "Cituu gabaasuuf odeeffannoo maal na barbaachisa?",
        answer: "Maqaa keessan, lakkoofsa bilbilaa, lakkoofsa CA/BP, fi teessoo cituu itti mudatee si barbaachisa. Bal'inni waa'ee haala sanaa gargaaraa dha."
      },
      {
        question: "Olloonni koo duraan gabaasanii yoo ta'e gabaasuu qabaa?",
        answer: "Jalqaba kutaa haala ilaalaatii naannoon keessan duraan gabaafamee jiraachuu fi dhiisuu ilaali. Yoo hin taane, maaloo gabaasaa - gabaasaleen hedduun bal'ina hubachuuf nu gargaaru."
      },
      {
        question: "Garaagarummaan lakkoofsota CA fi BP maal?",
        answer: "Lakkoofsoonni CA maamiltoota idileef lakkoofsota 12 dha. Lakkoofsoonni BP maamiltoota daldalaa/gabaa lakkoofsota 10 dha. Lamaan isaanii iyyuu herrega elektirikii keessan irratti argamuu danda'u."
      }
    ]
  },

  // Emergency Banner
  emergency: {
    title: "Akeekkachiisa Cituu Balaa",
    message: "Balaa humna elektirikii mudachaa jiraattan ykn sibiila humna kufee argaa jiraattan yoo ta'e, sarara bilbilaa balaa keenya yeruma sana bilbilaa.",
    phone: "Balaa: +251-11-123-4567",
    cta: "Amma Bilbilaa"
  },

  // Footer
  footer: {
    description: "Dhaabbatni Humna Elektirikii Itoophiyaa hawaasa Itoophiyaa hunda keessatti tajaajila humna amanamaa kennuuf kutateera.",
    quickLinks: "Hidhannoo Saffisaa",
    contact: "Odeeffannoo Quunnamtii",
    followUs: "Nu Hordofaa",
    links: {
      about: "Waa'ee EEU",
      services: "Tajaajila Keenya",
      news: "Oduu fi Fooyya'iinsa",
      careers: "Hojii",
      privacy: "Imaammata Dhuunfaa",
      terms: "Ulaagaalee Tajaajilaa"
    },
    contactInfo: {
      address: "Finfinnee, Itoophiyaa",
      phone: "+251-11-123-4567",
      email: "<EMAIL>"
    },
    copyright: "© 2024 Dhaabbata Humna Elektirikii Itoophiyaa. Mirgi hundi eegameera."
  },

  // Login
  login: {
    title: "Gara Herrega Keessaniitti Seenaa",
    email: "Teessoo Imeelii",
    password: "Jecha Icciitii",
    remember: "Na yaadadhu",
    forgot: "Jecha icciitii irraanfattan?",
    submit: "Seeni",
    noAccount: "Herrega hin qabdu?",
    signup: "Asitti galmaa'aa",
    errors: {
      email: "Maaloo teessoo imeelii sirrii galchaa",
      password: "Jechni icciitii barbaachisaa dha",
      invalid: "Imeelii ykn jechni icciitii sirrii miti"
    }
  },

  // Signup
  signup: {
    title: "Herrega Keessan Uumaa",
    name: "Maqaa Guutuu",
    email: "Teessoo Imeelii",
    phone: "Lakkoofsa Bilbilaa",
    password: "Jecha Icciitii",
    confirmPassword: "Jecha Icciitii Mirkaneessi",
    submit: "Herrega Uumi",
    hasAccount: "Duraan herrega qabdu?",
    login: "Asitti seenaa",
    errors: {
      name: "Maqaan barbaachisaa dha",
      email: "Maaloo teessoo imeelii sirrii galchaa",
      phone: "Maaloo lakkoofsa bilbilaa sirrii galchaa",
      password: "Jechni icciitii yoo xiqqaate arfii 6 ta'uu qaba",
      confirmPassword: "Jechoonni icciitii hin walsimne"
    }
  },

  // Common
  common: {
    loading: "Fe'aa jira...",
    error: "Dogoggora",
    success: "Milkaa'e",
    cancel: "Dhiisi",
    save: "Olkaa'i",
    edit: "Gulaal",
    delete: "Haqi",
    confirm: "Mirkaneessi",
    yes: "Eeyyee",
    no: "Lakki",
    ok: "Tole",
    close: "Cufi",
    back: "Deebi'i",
    next: "Itti Aansu",
    previous: "Dura",
    required: "Barbaachisaa",
    optional: "Filannoo"
  }
};
